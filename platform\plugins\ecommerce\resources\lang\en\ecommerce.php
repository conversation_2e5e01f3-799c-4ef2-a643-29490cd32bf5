<?php

return [
    'settings' => 'Settings',
    'name' => 'Ecommerce',
    'phone' => 'Phone',
    'email' => 'Email',
    'country' => 'Country',
    'state' => 'State',
    'city' => 'City',
    'address' => 'Address',
    'company' => 'Company',
    'tax_id' => 'Tax ID',
    'store_address' => 'Store address',
    'store_phone' => 'Store phone',
    'order_id' => 'Order ID',
    'order_token' => 'Order token',
    'customer_name' => 'Customer name',
    'customer_email' => 'Customer email',
    'customer_phone' => 'Customer phone',
    'customer_address' => 'Customer address',
    'product_list' => 'List products in order',
    'order_note' => 'Order note',
    'payment_detail' => 'Payment detail',
    'shipping_method' => 'Shipping method',
    'payment_method' => 'Payment method',
    'theme_options' => [
        'name' => 'Ecommerce',
        'description' => 'Theme options for Ecommerce',
        'product_listing_page_slug' => 'Product listing page slug',
        'product_listing_page_slug_instruction' => 'Default is "products", then URL will look like: https://example.com/products',
        'number_products_per_page' => 'Number of products per page',
        'number_of_cross_sale_product' => 'Number of cross sale products in product detail page',
        'max_price_filter' => 'Maximum price to filter',
        'logo_in_the_checkout_page' => 'Logo in the checkout page (Default is the main logo)',
        'login_background_image' => 'Login background image',
        'register_background_image' => 'Register background image',
        'term_and_privacy_policy_url' => 'Terms and Privacy Policy URL',
    ],
    'basic_settings' => 'Basic settings',
    'general_settings' => 'General',
    'general_setting_description' => 'View and update your general settings',
    'advanced_settings' => 'Advanced settings',
    'product_review_list' => 'Product review list',
    'forms' => [
        'duplicate' => 'Duplicate',
        'duplicate_success_message' => 'Duplicate product successfully!',
    ],
    'duplicate_modal' => 'Duplicate product',
    'duplicate_modal_description' => 'Are you sure you want to duplicate this product?',
];
