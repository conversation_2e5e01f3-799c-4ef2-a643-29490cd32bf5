<?php

namespace Bo<PERSON>ble\Ecommerce\Repositories\Eloquent;

use Botble\Ecommerce\Repositories\Interfaces\CurrencyInterface;
use Bo<PERSON>ble\Support\Repositories\Eloquent\RepositoriesAbstract;
use Illuminate\Database\Eloquent\Collection;

class CurrencyRepository extends RepositoriesAbstract implements CurrencyInterface
{
    public function getAllCurrencies(): Collection
    {
        $data = $this->model
            ->orderBy('order');

        return $this->applyBeforeExecuteQuery($data)->get();
    }
}
