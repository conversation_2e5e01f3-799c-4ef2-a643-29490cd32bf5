<?php

namespace Bo<PERSON>ble\Ecommerce\Http\Controllers\Settings;

use Botble\Ecommerce\Facades\EcommerceHelper;
use Bo<PERSON>ble\Setting\Http\Controllers\SettingController as BaseSettingController;

abstract class SettingController extends BaseSettingController
{
    protected function saveSettings(array $data, string $prefix = ''): void
    {
        parent::saveSettings($data, EcommerceHelper::getSettingPrefix());
    }
}
