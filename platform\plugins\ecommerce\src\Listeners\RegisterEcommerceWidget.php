<?php

namespace <PERSON><PERSON><PERSON>\Ecommerce\Listeners;

use <PERSON><PERSON>ble\Base\Events\RenderingAdminWidgetEvent;
use <PERSON><PERSON>ble\Ecommerce\Widgets\CustomerChart;
use <PERSON><PERSON>ble\Ecommerce\Widgets\NewCustomerCard;
use Bo<PERSON>ble\Ecommerce\Widgets\NewOrderCard;
use <PERSON><PERSON>ble\Ecommerce\Widgets\NewProductCard;
use Bo<PERSON>ble\Ecommerce\Widgets\OrderChart;
use Botble\Ecommerce\Widgets\RecentOrdersTable;
use Bo<PERSON>ble\Ecommerce\Widgets\ReportGeneralHtml;
use Botble\Ecommerce\Widgets\RevenueCard;
use Bo<PERSON>ble\Ecommerce\Widgets\TopSellingProductsTable;
use Bo<PERSON><PERSON>\Ecommerce\Widgets\TrendingProductsTable;
use Bo<PERSON>ble\Ecommerce\Widgets\UserProductsTable;

class RegisterEcommerceWidget
{
    public function handle(RenderingAdminWidgetEvent $event): void
    {
        $event->widget
            ->register([
                RevenueCard::class,
                NewProductCard::class,
                NewCustomerCard::class,
                NewOrderCard::class,
                CustomerChart::class,
                OrderChart::class,
                ReportGeneralHtml::class,
                RecentOrdersTable::class,
                UserProductsTable::class,
                TopSellingProductsTable::class,
                TrendingProductsTable::class,
            ], 'ecommerce');
    }
}
