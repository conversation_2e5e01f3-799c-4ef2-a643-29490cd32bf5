<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Listeners;

use <PERSON><PERSON>ble\Ecommerce\Events\OrderCreated;
use Bo<PERSON>ble\Ecommerce\Events\OrderPlacedEvent;
use Bo<PERSON>ble\Ecommerce\Facades\InvoiceHelper;

class GenerateInvoiceListener
{
    public function handle(OrderPlacedEvent|OrderCreated $event): void
    {
        $order = $event->order;

        InvoiceHelper::store($order);
    }
}
