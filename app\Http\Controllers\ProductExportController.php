<?php

namespace App\Http\Controllers;

use Botble\Ecommerce\Models\Product;
use Illuminate\Http\Response;

class ProductExportController extends Controller
{
    /**
     * Export all products to CSV format matching Product-example.csv structure
     *
     * @return Response
     */
    public function exportCsv()
    {
        try {
            // Test basic Product model access first
            $productCount = Product::count();

            if ($productCount === 0) {
                return response('No products found to export.', 404);
            }

            // Start with a simple query first
            $products = Product::limit(10)->get();

            // Create a simple CSV for testing
            $csvContent = "ID,Name,SKU\n";
            foreach ($products as $product) {
                $csvContent .= $product->id . ',' .
                              '"' . str_replace('"', '""', $product->name) . '",' .
                              $product->sku . "\n";
            }

            // Generate filename with current timestamp
            $filename = 'products_export_test_' . date('Y-m-d_H-i-s') . '.csv';

            // Return CSV file as downloadable response
            return response($csvContent)
                ->header('Content-Type', 'text/csv')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            // Return error details for debugging
            return response()->json([
                'error' => 'Database error: ' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }

        // Define CSV headers matching Product-example.csv exactly
        $headers = [
            'ID',
            'Name',
            'Description',
            'Slug',
            'URL',
            'SKU',
            'Categories',
            'Status',
            'Is Featured',
            'Brand',
            'Product Collections',
            'Labels',
            'Taxes',
            'Image',
            'Images',
            'Price',
            'Product Attributes',
            'Import Type',
            'Is Variation Default',
            'Stock Status',
            'With Storehouse Management',
            'Quantity',
            'Sale Price',
            'Start Date',
            'End Date',
            'Weight',
            'Length',
            'Wide',
            'Height',
            'Cost Per Item',
            'Barcode',
            'Content',
            'Tags',
            'Product Type',
            'Auto Generate Sku',
            'Generate License Code',
            'Minimum Order Quantity',
            'Maximum Order Quantity',
            'Vendor',
            'Name (AR)',
            'Description (AR)',
            'Content (AR)'
        ];

        // Create CSV content
        $csvContent = $this->generateCsvContent($products, $headers);

        // Generate filename with current timestamp
        $filename = 'products_export_' . date('Y-m-d_H-i-s') . '.csv';

        // Return CSV file as downloadable response
        return response($csvContent)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0');
    }

    /**
     * Generate CSV content from products data
     *
     * @param \Illuminate\Database\Eloquent\Collection $products
     * @param array $headers
     * @return string
     */
    private function generateCsvContent($products, $headers)
    {
        $output = fopen('php://temp', 'r+');

        // Add CSV headers
        fputcsv($output, $headers);

        // Add product data rows
        foreach ($products as $product) {
            $row = [
                $product->id,
                $product->name,
                $product->description,
                $product->slugable ? $product->slugable->key : '',
                $product->url ?? '', // URL - using the url attribute
                $product->sku,
                $this->formatCategories($product->categories),
                $product->status,
                $product->is_featured ? '1' : '0',
                $product->brand ? $product->brand->name : '',
                $this->formatProductCollections($product->productCollections),
                $this->formatProductLabels($product->productLabels),
                $this->formatTaxes($product),
                $product->image,
                $this->formatImages($product->images),
                $product->price,
                $this->formatProductAttributes($product->productAttributeSets),
                'product', // Import Type - default value
                $product->is_variation ? '0' : '1', // Is Variation Default
                $product->stock_status,
                $product->with_storehouse_management ? '1' : '0',
                $product->quantity,
                $product->sale_price,
                $product->start_date ? $product->start_date->format('Y-m-d') : '',
                $product->end_date ? $product->end_date->format('Y-m-d') : '',
                $product->weight,
                $product->length,
                $product->wide,
                $product->height,
                $product->cost_per_item,
                $product->barcode,
                $product->content,
                $this->formatTags($product->tags),
                $product->product_type ?? 'physical',
                '1', // Auto Generate Sku - default value
                $product->generate_license_code ? '1' : '0',
                '', // Minimum Order Quantity - not in model
                '', // Maximum Order Quantity - not in model
                '', // Vendor - not in model
                '', // Name (AR) - would need translation support
                '', // Description (AR) - would need translation support
                '', // Content (AR) - would need translation support
            ];
            fputcsv($output, $row);
        }

        rewind($output);
        $csvContent = stream_get_contents($output);
        fclose($output);

        return $csvContent;
    }

    /**
     * Format categories as comma-separated string
     */
    private function formatCategories($categories)
    {
        return $categories->pluck('name')->implode(',');
    }

    /**
     * Format product collections as comma-separated string
     */
    private function formatProductCollections($productCollections)
    {
        return $productCollections->pluck('name')->implode(',');
    }

    /**
     * Format product labels as comma-separated string
     */
    private function formatProductLabels($productLabels)
    {
        return $productLabels->pluck('name')->implode(',');
    }

    /**
     * Format taxes as comma-separated string
     */
    private function formatTaxes($product)
    {
        try {
            // Handle the special taxes relationship
            if ($product->tax) {
                return $product->tax->title;
            }
            return '';
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Format images array as comma-separated string
     */
    private function formatImages($images)
    {
        if (is_array($images)) {
            return implode(',', $images);
        }
        return '';
    }

    /**
     * Format product attributes as comma-separated string
     */
    private function formatProductAttributes($productAttributeSets)
    {
        return $productAttributeSets->pluck('title')->implode(',');
    }

    /**
     * Format tags as comma-separated string
     */
    private function formatTags($tags)
    {
        return $tags->pluck('name')->implode(',');
    }
}
