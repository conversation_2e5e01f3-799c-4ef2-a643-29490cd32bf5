@if ($users->isNotEmpty())
    <div class="table-responsive">
        <x-core::table>
            <x-core::table.header>
                <x-core::table.header.cell>
                    #
                </x-core::table.header.cell>
                <x-core::table.header.cell>
                    {{ trans('core/base::tables.name') }}
                </x-core::table.header.cell>
                <x-core::table.header.cell class="text-end">
                    {{ trans('core/base::tables.created_at') }}
                </x-core::table.header.cell>
            </x-core::table.header>

            <x-core::table.body>
                @foreach ($users as $user)
                    <x-core::table.body.row>
                        <x-core::table.body.cell>
                            {{ $user->index + 1 }}
                        </x-core::table.body.cell>
                        <x-core::table.body.cell>
                                <a href="#" >{{ $user->name }}</a>
                            @endif
                        </x-core::table.body.cell>
                        <x-core::table.body.cell class="text-end text-nowrap">
                            {{ $user->products->count() }}
                        </x-core::table.body.cell>
                    </x-core::table.body.row>
                @endforeach
            </x-core::table.body>
        </x-core::table>
    </div>
@else
    <x-core::empty-state
        :title="__('No results found')"
        :subtitle="trans('No users found')"
    />
@endif
