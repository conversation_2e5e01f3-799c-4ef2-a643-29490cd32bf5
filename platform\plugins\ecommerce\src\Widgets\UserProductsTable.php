<?php

namespace Bo<PERSON>ble\Ecommerce\Widgets;

use Bo<PERSON>ble\Base\Widgets\Table;
use Botble\Ecommerce\Tables\Reports\UserProductsTable as BaseTrendingProductsTable;

class UserProductsTable extends Table
{
    protected string $table = BaseTrendingProductsTable::class;

    protected string $route = 'ecommerce.report.user-products';

    protected int $columns = 6;

    public function getLabel(): string
    {
        return trans('User Product Count');
    }
}
