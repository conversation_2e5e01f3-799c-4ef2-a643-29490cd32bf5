<?php

use App\Http\Controllers\BrandExportController;
use App\Http\Controllers\ProductExportController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Brand CSV Export Route
Route::get('/brands/export-csv', [BrandExportController::class, 'exportCsv'])->name('brands.export.csv');

// Product CSV Export Route
Route::get('/products/export-csv', [ProductExportController::class, 'exportCsv'])->name('products.export.csv');

// Test route
Route::get('/test-route', function () {
    return response()->json(['message' => 'Test route works!', 'time' => now()]);
});

// Test Product model route
Route::get('/test-products', function () {
    try {
        $count = \Botble\Ecommerce\Models\Product::count();
        $first = \Botble\Ecommerce\Models\Product::first();
        return response()->json([
            'message' => 'Product model works!',
            'count' => $count,
            'first_product' => $first ? $first->name : 'No products'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ], 500);
    }
});
