<?php

use App\Http\Controllers\BrandExportController;
use App\Http\Controllers\ProductExportController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Brand CSV Export Route
Route::get('/brands/export-csv', [BrandExportController::class, 'exportCsv'])->name('brands.export.csv');

// Product CSV Export Route
Route::get('/products/export-csv', [ProductExportController::class, 'exportCsv'])->name('products.export.csv');
