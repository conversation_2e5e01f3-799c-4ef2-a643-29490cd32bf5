.ecommerce-status-list {
    margin-left: 10px;

    li {
        display: flex;
        align-items: center;
        width: 50%;
        float: left;
        padding: 0;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        margin: 0;
        border-top: 1px solid var(--bb-card-border-color);
        color: #aaa;

        a {
            display: block;
            color: #aaa;
            padding: 9px 12px;
            -webkit-transition: all ease .5s;
            transition: all ease .5s;
            position: relative;
            font-size: 12px;

            &:hover {
                color: #2ea2cc
            }

            strong {
                font-size: 18px;
                line-height: 1.2em;
                font-weight: 400;
                display: block;
                color: rgba(var(--bb-link-color-rgb),var(--bb-link-opacity,1));
            }
        }

        i {
            font-size: 30px;
            margin-left: 10px;
        }

        &.sales-this-month {
            width: 100%;

            i {
                color: #464646;
            }
        }

        &.processing-orders {
            border-right: 1px solid #ececec;

            i {
                color: #7ad03a
            }
        }

        &.completed-orders {
            i {
                color: #999;
            }
        }

        &.low-in-stock {
            border-right: 1px solid #ececec;

            i {
                color: #ffba00
            }
        }

        &.out-of-stock {
            i {
                color: #a00;
            }
        }
    }
}
