@use 'ultils' as *;

.#{$prefix}product {
    &-rating {
        --#{$prefix}rating-size: 80px;

        height: calc(var(--#{$prefix}rating-size) / 5);
        position: relative;
        width: var(--#{$prefix}rating-size);

        &:before {
            background-image: url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20stroke-width%3D%222%22%20stroke%3D%22currentColor%22%20fill%3D%22none%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%3E%3Cpath%20stroke%3D%22none%22%20d%3D%22M0%200h24v24H0z%22%20fill%3D%22none%22%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M8.243%207.34l-6.38%20.925l-.113%20.023a1%201%200%200%200%20-.44%201.684l4.622%204.499l-1.09%206.355l-.013%20.11a1%201%200%200%200%201.464%20.944l5.706%20-3l5.693%203l.1%20.046a1%201%200%200%200%201.352%20-1.1l-1.091%20-6.355l4.624%20-4.5l.078%20-.085a1%201%200%200%200%20-.633%20-1.62l-6.38%20-.926l-2.852%20-5.78a1%201%200%200%200%20-1.794%200l-2.853%205.78z%22%20stroke-width%3D%220%22%20fill%3D%22%23ced4da%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E');
            background-repeat: repeat-x;
            background-size: calc(var(--#{$prefix}rating-size) / 5);
            bottom: 0;
            content: '';
            display: block;
            height: calc(var(--#{$prefix}rating-size) / 5);
            inset-inline-start: 0;
            position: absolute;
            inset-inline-end: 0;
            top: 0;
            width: var(--#{$prefix}rating-size);
        }

        > span {
            display: block;
            width: var(--#{$prefix}rating-size);
            height: calc(var(--#{$prefix}rating-size) / 5);
            position: absolute;
            overflow: hidden;

            &:before {
                background-image: url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20stroke-width%3D%222%22%20stroke%3D%22currentColor%22%20fill%3D%22none%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%3E%3Cpath%20stroke%3D%22none%22%20d%3D%22M0%200h24v24H0z%22%20fill%3D%22none%22%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M8.243%207.34l-6.38%20.925l-.113%20.023a1%201%200%200%200%20-.44%201.684l4.622%204.499l-1.09%206.355l-.013%20.11a1%201%200%200%200%201.464%20.944l5.706%20-3l5.693%203l.1%20.046a1%201%200%200%200%201.352%20-1.1l-1.091%20-6.355l4.624%20-4.5l.078%20-.085a1%201%200%200%200%20-.633%20-1.62l-6.38%20-.926l-2.852%20-5.78a1%201%200%200%200%20-1.794%200l-2.853%205.78z%22%20stroke-width%3D%220%22%20fill%3D%22%23FFB342%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E');
                background-repeat: repeat-x;
                background-size: calc(var(--#{$prefix}rating-size) / 5);
                bottom: 0;
                content: '';
                display: block;
                height: calc(var(--#{$prefix}rating-size) / 5);
                inset-inline-start: 0;
                position: absolute;
                inset-inline-end: 0;
                top: 0;
            }
        }
    }

    &-attribute-swatch {
        margin-bottom: 1rem;

        &-title {
            font-size: 15px;
            font-weight: 400;
            margin-bottom: 0.5rem;
        }

        &-list {
            display: flex;
            flex-wrap: wrap;
            column-gap: 0.5rem;
            padding: 0;

            &.text-swatch {
                li {
                    list-style: none;

                    input[type='radio'],
                    input[type='checkbox'] {
                        display: none;

                        &:checked ~ span {
                            border: 1px solid var(--primary-color);

                            &:after {
                                width: 16px;
                                height: 16px;
                                content: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" stroke-width="2.2" stroke="white" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l5 5l10 -10" /></svg>');
                                position: absolute;
                                overflow: hidden;
                                inset-inline-end: -6px;
                                bottom: 1px;
                                color: #fff;
                            }

                            &:before {
                                border: 16px solid transparent;
                                border-bottom: 16px solid var(--primary-color);
                                content: '';
                                position: absolute;
                                inset-inline-end: -16px;
                                bottom: -1px;
                            }
                        }
                    }

                    span {
                        padding: 5px 15px;
                        border: 1px solid #ccc;
                        background-color: #ffffff;
                        cursor: pointer;
                        transition: 0.2s;
                        position: relative;
                        overflow: hidden;
                        display: inline-block;
                    }
                }
            }

            &.color-swatch {
                span {
                    position: absolute;
                    top: 50%;
                    inset-inline-start: 50%;
                    width: 100%;
                    height: 100%;
                    background-color: #fff;
                    transition: all 0.2s 0s linear;
                    transform: translate(-50%, -50%);
                }
            }

            &.visual-swatch {
                list-style: none;

                .#{$prefix}product-attribute-swatch-item {
                    position: relative;
                    border-radius: 50%;
                    box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.2);

                    &:hover,
                    &.active {
                        box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.2);
                        span {
                            transform: translate(-50%, -50%) scale(0.8);
                        }
                    }
                    &:hover {
                        & .#{$prefix}product-attribute-swatch-item-tooltip {
                            visibility: visible;
                            opacity: 1;
                            transform: translate(-50%) translateY(-10px);
                        }
                    }
                }

                label {
                    display: unset;
                }

                input[type='checkbox'],
                input[type='radio'] {
                    display: none;
                }

                span {
                    display: block;
                    border-radius: 50%;
                    width: 25px;
                    height: 25px;
                    position: relative;
                    cursor: pointer;
                }

                input[type='checkbox']:checked ~ span,
                input[type='radio']:checked ~ span {
                    &:before {
                        content: '';
                        display: block;
                        border: 2px solid var(--primary-color);
                        position: absolute;
                        top: -4px;
                        left: -4px;
                        right: -4px;
                        bottom: -4px;
                        border-radius: 50%;
                    }
                }

                &.disabled {
                    label {
                        position: relative;

                        &:before,
                        &:after {
                            content: '';
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            width: 1px;
                            height: 90%;
                            background-color: var(--primary-color);
                            transform-origin: 50% 50%;
                            transition: all 0.4s ease;
                            z-index: 20;
                        }

                        &:before {
                            transform: translate(-50%, -50%) rotate(45deg);
                        }

                        &:after {
                            transform: translate(-50%, -50%) rotate(-45deg);
                        }
                    }

                    span {
                        opacity: 0.9;
                    }

                    input[type='checkbox']:checked ~ span,
                    input[type='radio']:checked ~ span {
                        &:before {
                            display: none;
                        }
                    }
                }
            }
        }

        &-item {
            &.disabled {
                pointer-events: none;

                span {
                    color: gray;
                    cursor: unset;
                    opacity: 0.7;

                    &:before,
                    &:after {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 0;
                        width: 100%;
                        height: 0;
                        border-top: 1px dashed #999;
                    }

                    &:before {
                        -webkit-transform: rotate(45deg);
                        transform: rotate(45deg);
                    }

                    &:after {
                        -webkit-transform: rotate(-45deg);
                        transform: rotate(-45deg);
                    }
                }
            }

            &-tooltip {
                position: absolute;
                bottom: 100%;
                inset-inline-start: 50%;
                transform: translateX(-50%) translateY(2px);
                width: max-content;
                background-color: #000;
                color: #fff;
                text-align: center;
                font-size: 12px;
                font-weight: 500;
                line-height: 1;
                padding: 4px 6px;
                border-radius: 4px;
                visibility: hidden;
                opacity: 0;
                transition:
                    opacity 0.3s ease,
                    visibility 0.3s ease,
                    transform 0.3s cubic-bezier(0.71, 1.7, 0.77, 1.24);
                &::before {
                    position: absolute;
                    content: '';
                    bottom: -6px;
                    inset-inline-start: 50%;
                    transform: translateX(-50%);
                    width: 0;
                    height: 0;
                    border-top: 6px solid #000;
                    border-inline-start: 6px solid transparent;
                    border-inline-end: 6px solid transparent;
                }
            }
        }
    }

    &-gallery-images {
        width: 100%;

        .slick-slide {
            img {
                width: 100%;
            }
        }
    }

    &-thumbnails {
        padding: 0;
        margin: 0;
        margin-top: 12px;
        padding-left: 0;
        display: flex;
        align-items: center;
        gap: 1rem;

        .slick-slide {
            margin: 0 6px;
            position: relative;
            cursor: pointer;
            width: 78px;
            height: auto;

            &::after {
                position: absolute;
                content: '';
                width: 100%;
                height: 100%;
                background-color: transparent;
                border: 1px solid transparent;
                top: 0;
                left: 0;
                transition: all 0.3s ease-out 0s;
            }

            &.slick-current,
            &:hover {
                &::after {
                    border-color: #000;
                }
            }

            img {
                width: 100%;
                height: 100%;
                max-width: 100%;
                object-fit: cover;
                transition: all 0.2s 0s ease-out;
            }
        }
    }

    &-filter {
        margin-bottom: 30px;

        &-content {
            &:has(.bb-product-filter-items) {
                max-height: 288px;
                overflow-y: auto;
                overscroll-behavior-y: contain;
                scrollbar-width: thin;
            }
        }

        &-title {
            font-weight: 500;
            font-size: 18px;
            border-bottom: 1px solid #eeeeee;
            padding-bottom: 5px;
            margin-bottom: 25px;
        }

        &-items {
            padding-left: initial;

            & li {
                position: relative;
                list-style: none;

                &:not(:last-child) {
                    margin-bottom: 4px;
                }

                .#{$prefix}product-filter-items {
                    display: none;
                    padding-inline-start: 20px;
                }

                & input {
                    display: none;

                    &:checked {
                        & ~ label {
                            &::before {
                                visibility: visible;
                                opacity: 1;
                            }
                        }
                    }
                }

                label {
                    font-size: 16px;
                    color: #55585b;
                    position: relative;
                    padding-left: 26px;

                    &:hover {
                        cursor: pointer;
                    }

                    &::before {
                        position: absolute;
                        content: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="white" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l5 5l10 -10" /></svg>');
                        top: 5px;
                        left: 0;
                        width: 16px;
                        height: 16px;
                        line-height: 12px;
                        text-align: center;
                        visibility: hidden;
                        opacity: 0;
                        color: #fff;
                        transition: all 0.2s;
                        z-index: 1;
                    }
                }

                [data-bb-toggle='toggle-product-categories-tree'] {
                    margin-inline-end: 0.5rem;

                    svg {
                        height: 1rem;
                        width: 1rem;
                    }

                    &.active {
                        transform: rotate(90deg);
                    }
                }
            }

            &.filter-visual {
                & input {
                    &:checked {
                        & ~ label {
                            &::before {
                                visibility: visible;
                                opacity: 1;
                            }
                        }
                    }
                }

                .#{$prefix}product-filter-item {
                    span {
                        position: absolute;
                        content: '';
                        top: 2px;
                        left: 0;
                        width: 22px;
                        height: 22px;
                        line-height: 22px;
                        text-align: center;
                        z-index: -1;
                        border-radius: 50%;
                        transition: all 0.2s;
                    }

                    label {
                        padding-left: 30px;

                        &::before {
                            width: 22px;
                            height: 22px;
                        }
                    }
                }
            }

            &.filter-checkbox {
                & input {
                    &:checked {
                        & ~ label {
                            &::after {
                                background-color: var(--primary-color);
                                border-color: var(--primary-color);
                            }
                            &::before {
                                visibility: visible;
                                opacity: 1;
                            }
                        }
                    }
                }

                .#{$prefix}product-filter-item {
                    label {
                        &::after {
                            position: absolute;
                            content: '';
                            top: 5px;
                            left: 0;
                            width: 16px;
                            height: 16px;
                            line-height: 12px;
                            text-align: center;
                            border: 2px solid #dadada;
                            z-index: -1;
                            transition: all 0.2s;
                        }
                    }
                }
            }
        }
    }

    &-price-filter {
        .ui-widget.ui-widget-content {
            height: 3px;
            background-color: #ededed;
            border: 0;
        }

        .ui-slider-horizontal .ui-slider-range {
            background-color: var(--primary-color);
        }

        .ui-slider .ui-slider-handle {
            top: -7px;
            width: 5px;
            height: 17px;
            border: 0;
            padding: 0;
            margin: 0;
            border-radius: 0;
            background-color: var(--primary-color);
            cursor: pointer;
        }

        &-info {
            & .input-range {
                & input {
                    width: auto;
                    height: auto;
                    background-color: transparent;
                    color: #000;
                    padding: 0;
                    border: 0;
                    font-weight: 500;
                    font-size: 14px;
                }

                &-label {
                    color: #000;
                    font-weight: 500;
                    font-size: 14px;
                }
            }
            & .tp-shop-widget-filter-btn {
                font-weight: 400;
                font-size: 14px;
                color: #000;
                background-color: #f5f5f5;
                padding: 2px 21px;
                &:hover {
                    color: #fff;
                    background-color: #000;
                }
            }
        }
    }
}

.#{$prefix}filter-offcanvas {
    .#{$prefix}shop-sidebar {
        position: sticky;
        top: 120px;

        @media (max-width: 991px) {
            display: none;
        }
    }

    @media (max-width: 991px) {
        &-area {
            position: fixed;
            left: 0;
            top: 0;
            width: 340px;
            height: 100%;
            transform: translateX(calc(-100% - 80px));
            background: #fff none repeat scroll 0 0;
            transition: all 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86) 0s;
            z-index: 99999;

            overflow-y: scroll;
            overscroll-behavior-y: contain;
            scrollbar-width: none;

            &::-webkit-scrollbar {
                display: none; /* for Chrome, Safari, and Opera */
            }
            &.offcanvas-opened {
                transform: translateX(0);
                opacity: 1;

                .tp-shop-sidebar {
                    display: block;
                }
            }

            @media (max-width: 380px) {
                width: 100%;
            }
        }

        &-right {
            transform: translateX(calc(100% + 80px));
            left: auto;
            right: 0;
        }

        &-wrapper {
            position: relative;
            padding: 120px 30px 35px;
            z-index: 1;
            min-height: 100%;
        }
    }

    &-close {
        @media (min-width: 992px) {
            display: none;
        }

        &-btn {
            background: #151515;
            padding: 25px 30px;
            font-size: 18px;
            color: rgba($color: white, $alpha: 0.7);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;

            &:hover {
                color: #fff;
            }

            svg {
                width: 1.25rem;
                height: 1.25rem;
                stroke-width: 2;
            }
        }
    }
}

.#{$prefix}form-quick-search {
    position: relative;

    .#{$prefix}quick-search {
        &-results {
            position: absolute;
            background-color: #fff;
            width: 100%;
            top: 100%;
            opacity: 0;
            transition: all 0.4s ease;
            z-index: 10;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);

            &.show {
                opacity: 1;
            }
        }

        &-content {
            max-height: 30rem;
            overflow: auto;
        }

        &-item {
            display: flex;
            gap: 0.75rem;
            padding: 0.5rem 1.5rem 0.5rem 1rem;
            width: 100%;

            &:last-child {
                border-bottom: none;
            }

            &:hover {
                background-color: #f5f5f5;
            }

            &-image {
                flex: 0 0 auto;
                width: 15%;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            &-info {
                flex: 1 1 auto;
                width: 85%;
            }

            &-name {
                text-align: start;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-weight: 500;
            }

            &-rating {
                display: flex;
                align-items: center;
                gap: 0.25rem;

                > span {
                    font-size: 0.75rem;
                    color: #95999d;
                }
            }

            &-price {
                display: flex;
                align-items: center;

                .new-price {
                    font-weight: 500;
                    font-size: 15px;
                }

                .old-price {
                    color: #95999d;
                    text-decoration: line-through;
                    margin-left: 0.5rem;
                }
            }
        }

        &-empty {
            padding: 1rem;
            text-align: center;
        }

        &-load-more {
            padding: 1rem;
            text-align: center;

            a {
                font-weight: 500;
                font-size: 15px;
                text-decoration: underline;

                &:hover {
                    color: var(--primary-color);
                }
            }
        }

        &-view-all {
            padding: 1rem;
            text-align: center;
            border-top: 1px solid #eaeaef;

            a {
                font-weight: 500;
                font-size: 15px;
                color: var(--primary-color);

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
}

.#{$prefix}store {
    &-item {
        &-content {
            padding: 1rem 1.5rem;

            p {
                margin-bottom: 0.35rem;

                svg {
                    width: 1.25rem;
                    height: 1.25rem;
                }
            }
        }

        &-rating {
            margin-bottom: 0.5rem;
        }

        &-footer {
            position: relative;
            padding: 15px 20px;
            border-top: 1px solid #eee;
        }

        &-logo {
            padding: 6px;
            background: #fff;
            position: absolute;
            width: 80px;
            height: 80px;
            top: -3.5rem;
            right: 20px;
            border-radius: 40px;
            box-shadow: 0px 0px 30px -6px #afafaf;

            img {
                width: 100%;
                height: 100%;
                border-radius: 40px;
            }
        }

        &-action {
            a {
                display: flex;
                align-items: center;
                gap: 0.25rem;
                width: fit-content;

                svg {
                    width: 1.25rem;
                    height: 1.25rem;
                }
            }
        }
    }
}

.#{$prefix}shop-detail {
    .#{$prefix}shop-banner {
        position: relative;
        padding: 70px 10px 10px;
        min-height: 300px;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        margin-bottom: 5rem;
        background-color: rgba(var(--bs-secondary-rgb), 1);
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center;
        border-radius: var(--bs-border-radius);

        &-rating {
            margin-bottom: 0.5rem;
        }

        &-content {
            opacity: 0.95;
        }

        &-info {
            p {
                margin-bottom: 0.25rem;

                a {
                    &:hover {
                        color: var(--primary-color);
                    }
                }
            }
        }

        &-logo {
            width: 80px;
            border-radius: 50%;

            @media (min-width: 768px) {
                width: 120px;
            }
        }

        &-name {
            font-size: 22px;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        &-socials {
            li {
                a {
                    border-radius: var(--bs-border-radius);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 0.25rem;

                    &:hover {
                        background-color: var(--primary-color);
                        color: #fff;
                    }

                    svg {
                        width: 1.25rem;
                        height: 1.25rem;
                    }
                }
            }
        }
    }
}

.#{$prefix}customer {
    &-profile {
        display: flex;
        align-items: start;
        gap: 1rem;

        &-avatar {
            position: relative;
            width: 80px;
            height: 80px;
            flex-shrink: 0;

            img {
                border-radius: 50%;
                width: 80px;
                height: 80px;
                object-fit: cover;
            }

            &-overlay {
                input {
                    display: none;
                }

                label {
                    background-color: var(--primary-color);
                    border: 2px solid #fff;
                    border-radius: 50%;
                    color: #fff;
                    height: 30px;
                    position: absolute;
                    width: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    bottom: 8px;
                    inset-inline-end: -8px;

                    svg {
                        width: 1rem;
                        height: 1rem;
                    }
                }
            }
        }

        &-info {
            h4 {
                font-size: 18px;
                font-weight: 500;
                margin-bottom: 0.5rem;
            }

            p {
                margin-bottom: 0;
            }
        }
    }
}

.lg {
    .lg-thumb.lg-group {
        margin: 0 auto;
    }
}

body[dir='rtl'] {
    .lg-outer {
        direction: ltr;
    }
}
