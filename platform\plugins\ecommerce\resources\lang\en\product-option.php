<?php

return [
    'name' => 'Product options',
    'options' => 'Options',
    'create' => 'New option',
    'edit' => 'Edit option :name',
    'required' => 'Is required?',
    'option_value' => 'Option value',
    'option_type' => 'Type',
    'label' => 'Label',
    'price' => 'Price',
    'price_type' => 'Price Type',
    'percent' => 'Percent',
    'fixed' => 'Fixed',
    'label_placeholder' => 'Please fill label',
    'affect_price_label' => 'Please fill affect price',
    'add_new_row' => 'Add new row',
    'add_new_option' => 'Add new option',
    'select_global_option' => 'Select Global Option',
    'add_global_option' => 'Add Global Option',
    'please_select_option' => 'Please select option',
    'add_to_cart_value_required' => 'Option :value is required',
    'option_value_attribute' => 'value :item of :value_key on option #:option_key',
    'option_name_attribute' => 'Option :key',
    'option_type_attribute' => 'Type of option :key',
    'option_value_name_attribute' => 'Values of option :key',
    'please_choose_option_type' => 'Please choose option type!',
];
