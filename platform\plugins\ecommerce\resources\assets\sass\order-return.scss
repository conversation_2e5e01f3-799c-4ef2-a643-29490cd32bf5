.product-button {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
}

.product-button .quantity {
    flex: 0 1 100px;
    margin-bottom: 0.5rem;
    margin-right: 0.5rem;
}

.product-button .quantity .qty-box {
    border: 1px solid #ddd;
    border-radius: 3px;
    position: relative;
    width: 100%;
}

.product-button .quantity .decrease,
.product-button .quantity .increase {
    align-items: center;
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px;
    color: #000;
    cursor: pointer;
    display: flex;
    font-size: 10px;
    font-weight: 700;
    height: 100%;
    justify-content: center;
    left: 0;
    position: absolute;
    text-align: center;
    top: 0;
    transition: 0.5s;
    width: 30px;
    margin: 0;
    min-width: 0;
}

.product-button .quantity .increase {
    right: 0;
    left: auto;
}

.product-button .quantity .increase svg,
.product-button .quantity .decrease svg {
    fill: currentColor;
    display: inline-block;
    height: 1em;
    vertical-align: -0.125em;
    width: 1em;
}

.product-button .label-quantity {
    display: none;
    margin-bottom: 10px;
}
.product-button .quantity .increase {
    left: auto;
    right: 0;
}

.product-button .quantity .qty {
    -moz-appearance: textfield;
    appearance: textfield;
    -webkit-appearance: textfield;
    background-color: #fff;
    border-radius: 3px;
    border-width: 0;
    color: #000;
    display: inline-block;
    font-size: 15px;
    font-weight: 700;
    height: 38px;
    line-height: 38px;
    padding: 10px 0;
    text-align: center;
    width: 100%;
}

.customer-order-detail .form-select {
    padding: 8px 15px;
    height: auto;
    background-color: #fff;
}
