<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Tables\Reports;

use Bo<PERSON>ble\ACL\Models\User;
use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Ecommerce\Facades\EcommerceHelper as EcommerceHelper;
use Botble\Ecommerce\Models\Product;
use Botble\Ecommerce\Models\ProductView;
use Botble\Table\Abstracts\TableAbstract;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\IdColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class UserProductsTable extends TableAbstract
{
    public function setup(): void
    {
        $this->model(User::class);

        $this->view = $this->simpleTableView();
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('name', function (User $user) {
                return Html::link('#', $user->name, ['target' => '_blank']);
            })
            ->editColumn('views', function (User $user) {
                return $this->getProductsCount($user);
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport(request());

        $query = $this
            ->getModel()
            ->query()
            ->limit(5);

        return $this->applyScopes($query);
    }

    public function getColumns(): array
    {
        return $this->columns();
    }

    public function columns(): array
    {
        return [
            IdColumn::make(),
            Column::make('name')
                ->title(trans('User'))
                ->alignStart()
                ->orderable(false)
                ->searchable(false),
            Column::make('views')
                ->title(trans('Number of Products'))
                ->alignEnd()
                ->orderable(false)
                ->searchable(false),
        ];
    }

    public function isSimpleTable(): bool
    {
        return true;
    }

    public function getProductsCount(User $user): int
    {
        return Product::query()
            ->where('created_by_id', $user->getKey())
            ->count();
    }
}
