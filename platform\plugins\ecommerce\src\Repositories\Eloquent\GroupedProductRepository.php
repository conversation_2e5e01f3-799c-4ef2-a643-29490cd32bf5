<?php

namespace Bo<PERSON>ble\Ecommerce\Repositories\Eloquent;

use Botble\Ecommerce\Models\GroupedProduct;
use Bo<PERSON>ble\Ecommerce\Repositories\Interfaces\GroupedProductInterface;
use <PERSON><PERSON>ble\Support\Repositories\Eloquent\RepositoriesAbstract;

class GroupedProductRepository extends RepositoriesAbstract implements GroupedProductInterface
{
    public function getChildren($groupedProductId, array $params)
    {
        return GroupedProduct::getChildren($groupedProductId);
    }

    public function createGroupedProducts($groupedProductId, array $childItems)
    {
        return GroupedProduct::createGroupedProducts($groupedProductId, $childItems);
    }
}
