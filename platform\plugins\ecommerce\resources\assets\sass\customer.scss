@import 'cropper';

$primaryColor: var(--primary-color);

.customer-page {
    margin: 0;
    background-color: #ffffff;

    .customer-body {
        padding: 50px 20px;
    }

    .border-border {
        border: 1px solid #cecece;
    }

    .userpic-avatar {
        border: 2px solid #ffffff;
        border-radius: 50%;
        width: 70%;
        margin: 0 auto;
    }

    .profile-sidebar {
        padding: 20px;
        border-right: 1px solid #e2e2e2;
        color: rgb(228, 228, 228);
        height: 100%;
        background-color: #ffffff;
        box-shadow: 5px 5px 5px;
        -moz-box-shadow: 5px 5px 5px;
        -webkit-box-shadow: 5px 5px 5px;
        -o-box-shadow: 5px 5px 5px;

        .profile-usermenu {
            list-style: none;
            display: block;

            li.list-group-item {
                display: block;
                border-top: 0;
                border-left: 0;
                border-right: 0;
                border-bottom: 1px dashed #cecece;
                margin-bottom: 1px;

                i {
                    float: right;
                    color: rgb(82, 82, 82);
                    line-height: 25px;
                    font-size: 1.1em;
                }

                a {
                    color: rgb(0, 0, 0);
                    padding-right: 30px;

                    &:hover {
                        color: $primaryColor;
                    }

                    &.active, &:active {
                        text-decoration: none;
                        color: $primaryColor;
                        font-weight: bold;
                    }
                }
            }
        }

        .profile-customer-name {
            font-size: 1em;
            margin: 20px 0;
            text-align: center;
            color: $primaryColor;
        }
    }

    .profile-content {
        padding: 20px 20px 20px 50px;
    }

    h2.customer-page-title {
        text-align: start;
        font-size: 1.4em;
        font-weight: bold;
    }

    .customer-list-order {
        margin-top: 40px;
    }

    .customer-order-detail {
        p {
            margin: 0;
        }

        span {
            min-width: 150px;
            display: inline-block;
            margin: 5px 0;

            &.order-detail-value {
                padding: 5px;
                line-height: 10px;
                border-bottom: 1px dashed #cecece;
                margin-left: 20px;
                color: #000000;
                font-weight: bold;
                display: inline-block;
            }

            h5 {
                text-align: center;
                margin: 30px 0;
                width: 100%;
            }
        }
    }

    .dashboard-address {
        margin-bottom: 20px;

        .card {
            background-color: #f8f8f8;
            border: none;

            p {
                font-size: 85%;
            }
        }
    }


    .badge {
        color: #fff;
        min-width: 0 !important;
    }

    .show-admin-bar {
        .modal {
            top: 80px;
        }
    }

    .pagination {
        .page-item {
            .page-link {
                color: $primaryColor;
            }

            &.active {
                .page-link {
                    background-color: $primaryColor;
                    border-color: $primaryColor;
                    color: #fff;
                }
            }
        }
    }

    .btn-primary {
        background-color: $primaryColor;
        border-color: $primaryColor;
        position: relative;
        padding: 5px 12px;

        &::before {
            position: absolute;
            content: '';
            background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 100%);
            inset-inline-start: -10%;
            top: 0;
            height: 100%;
            width: 10%;
        }

        &:hover {
            &::before {
                animation: light-white 0.8s;
            }

            & i {
                animation: icon-arrow 0.4s linear;
            }
        }

        &:hover {
            background-color: $primaryColor;
            border-color: $primaryColor;
        }
    }

    svg.icon {
        stroke-width: 1;
        margin-bottom: 0;
    }

    table.table.table-bordered.table-striped {
        td, th {
            vertical-align: middle;
        }
    }

    img.img-fluid.rounded-start.ecommerce-product-image {
        max-height: 100px;
    }

    .gap-2 {
        gap: 0.5rem!important;
    }
}

svg.icon {
    stroke-width: 1;
    margin-bottom: 0;
}

@keyframes light-white {
    to {
        inset-inline-start: 100%
    }
}

@keyframes icon-arrow {
    49% {
        transform: translateX(30%);
    }
    50% {
        opacity: 0;
        transform: translateX(-30%);
    }
    51% {
        opacity: 1;
    }
}

