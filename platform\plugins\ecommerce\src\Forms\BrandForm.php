<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Forms;

use Bo<PERSON>ble\Base\Forms\FieldOptions\NameFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\OnOffFieldOption;
use Botble\Base\Forms\FieldOptions\SelectFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\StatusFieldOption;
use Botble\Base\Forms\Fields\OnOffField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\Fields\TreeCategoryField;
use Botble\Base\Forms\FormAbstract;
use Botble\Ecommerce\Facades\ProductCategoryHelper;
use Botble\Ecommerce\Http\Requests\BrandRequest;
use Botble\Ecommerce\Models\Brand;

class BrandForm extends FormAbstract
{
    public function setup(): void
    {
        $selected_type = null;
        
        if ($this->getModel()) {
            $selected_type = $this->getModel()->product_main_type;
        }
        $this
            ->setupModel(new Brand())
            ->setValidatorClass(BrandRequest::class)
            ->add('name', TextField::class, NameFieldOption::make()->toArray())
            ->add('short_description','textarea', [
                'label' => 'Short Description',
                'attr' => [
                    'rows' => 2,
                    'placeholder' => trans('plugins/ecommerce::products.form.description'),
                    'data-counter' => 200,
                ],
            ])
            ->add('description', 'editor', [
                'label' => trans('core/base::forms.description'),
                'attr' => [
                    'rows' => 6,
                    'placeholder' => trans('plugins/ecommerce::products.form.description'),
                    'data-counter' => 20000,
                ],
            ])
            ->add('model_list', 'editor', [
                'label' => 'Model List',
                'attr' => [
                    'rows' => 6,
                    'placeholder' => 'Model List',
                    'data-counter' => 20000,
                ],
            ])
            ->add('website', 'text', [
                'label' => trans('plugins/ecommerce::brands.form.website'),
                'attr' => [
                    'placeholder' => 'Ex: https://example.com',
                    'data-counter' => 120,
                ],
            ])
            ->add('order', 'number', [
                'label' => trans('core/base::forms.order'),
                'attr' => [
                    'placeholder' => trans('core/base::forms.order_by_placeholder'),
                ],
                'default_value' => 0,
            ])
            ->add('status', SelectField::class, StatusFieldOption::make()->toArray())
            ->add('logo', 'mediaImage', [
                'label' => trans('plugins/ecommerce::brands.logo'),
            ])
            ->add('second_logo', 'mediaImage', [
                'label' => trans('plugins/ecommerce::brands.logo'),
            ])
            ->add('horizontal_banner', 'mediaImage', [
                'label' => 'Banner',
            ])
            ->add(
                'is_featured',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('core/base::forms.is_featured'))
                    ->defaultValue(false)
                    ->toArray()
            )
            ->add(
                'product_main_type',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('core/base::forms.type'))
                    ->choices(['Industrial','Heavy Equipement', 'Marine'])
                    ->selected($selected_type)
                    ->toArray()
            )
            ->add(
                'categories[]',
                TreeCategoryField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/ecommerce::products.form.categories'))
                    ->choices(ProductCategoryHelper::getActiveTreeCategories())
                    ->selected($this->getModel()->id ? $this->getModel()->categories->pluck('id')->all() : [])
                    ->addAttribute('card-body-class', 'p-0')
                    ->toArray()
            )
            ->setBreakFieldPoint('status');
    }
}
