<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Listeners;

use <PERSON><PERSON><PERSON>\Base\Facades\EmailHandler;
use Bo<PERSON>ble\Ecommerce\Facades\EcommerceHelper;
use Bo<PERSON>ble\Ecommerce\Models\Customer;
use Illuminate\Auth\Events\Registered;

class SendMailsAfterCustomerRegistered
{
    public function handle(Registered $event): void
    {
        $customer = $event->user;

        if (! $customer instanceof Customer) {
            return;
        }

        EmailHandler::setModule(ECOMMERCE_MODULE_SCREEN_NAME)
            ->setVariableValues([
                'customer_name' => $customer->name,
            ])
            ->sendUsingTemplate('welcome', $customer->email);

        if (EcommerceHelper::isEnableEmailVerification()) {
            $customer->sendEmailVerificationNotification();
        }
    }
}
