<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Database\Seeders;

use Bo<PERSON>ble\Base\Supports\BaseSeeder;
use Bo<PERSON>ble\Ecommerce\Enums\ShippingRuleTypeEnum;
use <PERSON><PERSON>ble\Ecommerce\Models\Shipping;
use Botble\Ecommerce\Models\ShippingRule;
use Bo<PERSON>ble\Ecommerce\Models\ShippingRuleItem;

class ShippingSeeder extends BaseSeeder
{
    public function run(): void
    {
        Shipping::query()->truncate();
        ShippingRule::query()->truncate();
        ShippingRuleItem::query()->truncate();

        $shipping = Shipping::query()->create(['title' => 'All']);

        ShippingRule::query()->create([
            'name' => 'Free delivery',
            'shipping_id' => $shipping->getKey(),
            'type' => ShippingRuleTypeEnum::BASED_ON_PRICE,
            'from' => 0,
            'to' => null,
            'price' => 0,
        ]);
    }
}
