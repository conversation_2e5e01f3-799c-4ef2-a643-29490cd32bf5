<?php

namespace App\Http\Controllers;

use Botble\Ecommerce\Models\Brand;
use Illuminate\Http\Response;

class BrandExportController extends Controller
{
    /**
     * Export all brands to CSV format
     *
     * @return Response
     */
    public function exportCsv()
    {
        // Retrieve all Brand records from the database
        $brands = Brand::all();

        // Define CSV headers based on Brand model fillable fields
        $headers = [
            'ID',
            'Name',
            'Website',
            'Logo',
            'Second Logo',
            'Horizontal Banner',
            'Description',
            'Model List',
            'Short Description',
            'Order',
            'Is Featured',
            'Status',
            'Product Main Type',
            'Created At',
            'Updated At'
        ];

        // Create CSV content
        $csvContent = $this->generateCsvContent($brands, $headers);

        // Generate filename with current timestamp
        $filename = 'brands_export_' . date('Y-m-d_H-i-s') . '.csv';

        // Return CSV file as downloadable response
        return response($csvContent)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0');
    }

    /**
     * Generate CSV content from brands data
     *
     * @param \Illuminate\Database\Eloquent\Collection $brands
     * @param array $headers
     * @return string
     */
    private function generateCsvContent($brands, $headers)
    {
        $output = fopen('php://temp', 'r+');

        // Add CSV headers
        fputcsv($output, $headers);

        // Add brand data rows
        foreach ($brands as $brand) {
            $row = [
                $brand->id,
                $brand->name,
                $brand->website,
                $brand->logo,
                $brand->second_logo,
                $brand->horizontal_banner,
                $brand->description,
                $brand->model_list,
                $brand->short_description,
                $brand->order,
                $brand->is_featured ? 'Yes' : 'No',
                $brand->status,
                $brand->product_main_type,
                $brand->created_at ? $brand->created_at->format('Y-m-d H:i:s') : '',
                $brand->updated_at ? $brand->updated_at->format('Y-m-d H:i:s') : ''
            ];
            fputcsv($output, $row);
        }

        rewind($output);
        $csvContent = stream_get_contents($output);
        fclose($output);

        return $csvContent;
    }
}
