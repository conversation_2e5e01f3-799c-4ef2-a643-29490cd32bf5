<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Listeners;

use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Bo<PERSON>ble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Ecommerce\Models\Shipping;
use Bo<PERSON>ble\Ecommerce\Models\ShippingRule;
use Botble\Ecommerce\Services\HandleShippingFeeService;

class ClearShippingRuleCache
{
    public function __construct(protected HandleShippingFeeService $shippingFeeService)
    {
    }

    public function handle(CreatedContentEvent|UpdatedContentEvent|DeletedContentEvent $event): void
    {
        if (! in_array(get_class($event->data), [Shipping::class, ShippingRule::class])) {
            return;
        }

        $this->shippingFeeService->clearCache();
    }
}
