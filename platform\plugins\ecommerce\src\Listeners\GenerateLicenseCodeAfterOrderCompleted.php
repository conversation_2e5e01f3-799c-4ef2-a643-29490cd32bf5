<?php

namespace Bo<PERSON>ble\Ecommerce\Listeners;

use Bo<PERSON>ble\Ecommerce\Events\OrderCompletedEvent;
use Bo<PERSON>ble\Ecommerce\Models\Order;
use Bo<PERSON>ble\Ecommerce\Models\Product;
use Illuminate\Support\Str;

class GenerateLicenseCodeAfterOrderCompleted
{
    public function handle(OrderCompletedEvent $event): void
    {
        if (($order = $event->order) instanceof Order && $order->loadMissing(['products.product'])) {
            $orderProducts = $order->products
                ->where(function ($item) {
                    return $item->product->isTypeDigital() && $item->product->generate_license_code;
                });

            $invoiceItems = $order->invoice->items;
            foreach ($orderProducts as $orderProduct) {
                $licenseCode = Str::uuid();
                $orderProduct->license_code = $licenseCode;
                $orderProduct->save();

                $invoiceItem = $invoiceItems->where('reference_id', $orderProduct->product_id)->where('reference_type', Product::class)->first();
                if ($invoiceItem) {
                    $invoiceItem->options = array_merge($invoiceItem->options, [
                        'license_code' => $licenseCode,
                    ]);
                    $invoiceItem->save();
                }
            }
        }
    }
}
