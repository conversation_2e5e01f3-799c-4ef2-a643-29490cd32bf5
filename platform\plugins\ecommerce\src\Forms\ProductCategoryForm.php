<?php

namespace Bo<PERSON>ble\Ecommerce\Forms;

use Botble\Base\Forms\FieldOptions\ContentFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\NameFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\OnOffFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\StatusFieldOption;
use Botble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\Fields\EditorField;
use Botble\Base\Forms\Fields\OnOffField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\FormAbstract;
use Botble\Ecommerce\Http\Requests\ProductCategoryRequest;
use Botble\Ecommerce\Models\ProductCategory;

class ProductCategoryForm extends FormAbstract
{
    public function setup(): void
    {
        $selected_type = null;
        
        if ($this->getModel()) {
           
            $selected_type = $this->getModel()->product_main_type;
        }
        
        $this
            ->setupModel(new ProductCategory())
            ->setValidatorClass(ProductCategoryRequest::class)
            ->add('name', TextField::class, NameFieldOption::make()->toArray())
            ->add(
                'description',
                EditorField::class,
                ContentFieldOption::make()->label(trans('core/base::forms.description'))->toArray()
            )
            ->add('status', SelectField::class, StatusFieldOption::make()->toArray())
            ->add(
                'product_main_type',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('core/base::forms.type'))
                    ->choices(['Industrial','Heavy Equipement', 'Marine'])
                    ->selected($selected_type)
                    ->toArray()
            )
            ->add('image', 'mediaImage')
            ->add(
                'icon',
                $this->getFormHelper()->hasCustomField('themeIcon') ? 'themeIcon' : 'text',
                [
                    'label' => __('Font Icon'),
                    'attr' => [
                        'placeholder' => 'ex: fa fa-home',
                    ],
                    'empty_value' => __('-- None --'),
                ]
            )
            ->add('icon_image', 'mediaImage', [
                'label' => __('Icon image'),
                'help_block' => [
                    'text' => __('It will replace Icon Font if it is present.'),
                ],
                'wrapper' => [
                    'style' => 'display: block;',
                ],
            ])
            ->add(
                'is_featured',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('core/base::forms.is_featured'))
                    ->defaultValue(false)
                    ->toArray()
            )
            
            ->setBreakFieldPoint('status');
    }
}
